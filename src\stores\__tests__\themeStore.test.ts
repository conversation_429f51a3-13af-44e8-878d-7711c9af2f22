import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useThemeStore } from '../themeStore';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe('themeStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store state
    useThemeStore.setState({
      theme: 'system',
      colors: {
        primary: '#3b82f6',
        secondary: '#6366f1',
        accent: '#8b5cf6',
        neutral: '#64748b',
        success: '#059669',
        warning: '#d97706',
        error: '#dc2626',
        background: '#ffffff',
        surface: '#f8fafc',
        surfaceSecondary: '#f1f5f9',
        text: '#0f172a',
        textSecondary: '#64748b',
        textMuted: '#94a3b8',
        border: '#e2e8f0',
        borderSecondary: '#cbd5e1',
        shadow: '#00000010',
      },
    });
  });

  it('initializes with system theme', () => {
    const { theme } = useThemeStore.getState();
    expect(theme).toBe('system');
  });

  it('sets light theme correctly', () => {
    const { setTheme } = useThemeStore.getState();
    setTheme('light');

    const { theme, colors } = useThemeStore.getState();
    expect(theme).toBe('light');
    expect(colors.background).toBe('#ffffff');
    expect(colors.text).toBe('#0f172a');
  });

  it('sets dark theme correctly', () => {
    const { setTheme } = useThemeStore.getState();
    setTheme('dark');

    const { theme, colors } = useThemeStore.getState();
    expect(theme).toBe('dark');
    expect(colors.background).toBe('#0f172a');
    expect(colors.text).toBe('#f8fafc');
  });

  it('toggles between light and dark themes', () => {
    const { setTheme, toggleTheme } = useThemeStore.getState();

    setTheme('light');
    toggleTheme();
    expect(useThemeStore.getState().theme).toBe('dark');

    toggleTheme();
    expect(useThemeStore.getState().theme).toBe('light');
  });
});
