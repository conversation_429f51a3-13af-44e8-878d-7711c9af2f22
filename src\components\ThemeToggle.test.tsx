import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ThemeToggle } from './ThemeToggle';

// Mock the theme store
const mockSetTheme = vi.fn();
vi.mock('../stores/themeStore', () => ({
  useThemeStore: () => ({
    theme: 'light',
    setTheme: mockSetTheme,
    colors: {
      primary: '#2563eb',
      background: '#ffffff',
      text: '#111827',
    },
  }),
}));

describe('ThemeToggle', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders theme toggle button', () => {
    render(<ThemeToggle />);

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute(
      'aria-label',
      expect.stringContaining('Switch to next theme')
    );
  });

  it('cycles through themes when clicked', async () => {
    const user = userEvent.setup();
    render(<ThemeToggle />);

    const button = screen.getByRole('button');

    // First click should switch to dark
    await user.click(button);

    await waitFor(() => {
      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });
  });

  it('shows label when showLabel is true', () => {
    render(<ThemeToggle showLabel={true} />);

    expect(screen.getByText(/light mode/i)).toBeInTheDocument();
  });

  it('hides label when showLabel is false', () => {
    render(<ThemeToggle showLabel={false} />);

    expect(screen.queryByText(/light mode/i)).not.toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<ThemeToggle size="sm" />);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('w-8', 'h-8');

    rerender(<ThemeToggle size="md" />);
    button = screen.getByRole('button');
    expect(button).toHaveClass('w-10', 'h-10');

    rerender(<ThemeToggle size="lg" />);
    button = screen.getByRole('button');
    expect(button).toHaveClass('w-12', 'h-12');
  });

  it('is disabled during animation', async () => {
    const user = userEvent.setup();
    render(<ThemeToggle />);

    const button = screen.getByRole('button');

    // Click to start animation
    await user.click(button);

    // Button should be temporarily disabled
    expect(button).toBeDisabled();

    // Wait for animation to complete
    await waitFor(
      () => {
        expect(button).not.toBeDisabled();
      },
      { timeout: 200 }
    );
  });

  it('has proper accessibility attributes', () => {
    render(<ThemeToggle />);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label');
    expect(button).toHaveAttribute('title');
  });

  it('applies custom className', () => {
    render(<ThemeToggle className="custom-class" />);

    const container = screen.getByRole('button').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('has correct test id', () => {
    render(<ThemeToggle data-testid="theme-toggle" />);

    expect(screen.getByTestId('theme-toggle')).toBeInTheDocument();
  });
});

// Test with different theme states
describe('ThemeToggle with different themes', () => {
  it('shows correct icon for light theme', () => {
    const mockUseThemeStore = vi.fn().mockReturnValue({
      theme: 'light',
      setTheme: vi.fn(),
      colors: { primary: '#2563eb' },
    });
    vi.doMock('../stores/themeStore', () => ({
      useThemeStore: mockUseThemeStore,
    }));

    render(<ThemeToggle />);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute(
      'aria-label',
      expect.stringContaining('Light Mode')
    );
  });

  it('shows correct icon for dark theme', () => {
    const mockUseThemeStore = vi.fn().mockReturnValue({
      theme: 'dark',
      setTheme: vi.fn(),
      colors: { primary: '#2563eb' },
    });
    vi.doMock('../stores/themeStore', () => ({
      useThemeStore: mockUseThemeStore,
    }));

    render(<ThemeToggle />);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute(
      'aria-label',
      expect.stringContaining('Dark Mode')
    );
  });

  it('shows correct icon for system theme', () => {
    const mockUseThemeStore = vi.fn().mockReturnValue({
      theme: 'system',
      setTheme: vi.fn(),
      colors: { primary: '#2563eb' },
    });
    vi.doMock('../stores/themeStore', () => ({
      useThemeStore: mockUseThemeStore,
    }));

    render(<ThemeToggle />);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute(
      'aria-label',
      expect.stringContaining('System')
    );
  });
});
