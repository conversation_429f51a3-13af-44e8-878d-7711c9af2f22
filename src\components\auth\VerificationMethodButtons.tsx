import React, { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';

export interface VerificationMethod {
  id: 'whatsapp' | 'sms' | 'call';
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  hoverColor: string;
  available?: boolean;
}

export interface VerificationMethodButtonsProps {
  selectedMethod?: 'whatsapp' | 'sms' | 'call';
  onMethodSelect: (method: 'whatsapp' | 'sms' | 'call') => void;
  phoneNumber?: string;
  disabled?: boolean;
  loading?: boolean;
  layout?: 'horizontal' | 'vertical' | 'grid';
  showDescriptions?: boolean;
  className?: string;
  'data-testid'?: string;
}

const verificationMethods: VerificationMethod[] = [
  {
    id: 'whatsapp',
    label: 'WhatsApp',
    description: 'Secure verification via WhatsApp',
    color: '#25D366',
    hoverColor: '#1DA851',
    icon: (
      <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
      </svg>
    ),
    available: true,
  },
  {
    id: 'sms',
    label: 'SMS',
    description: 'Text message verification',
    color: '#2563eb',
    hoverColor: '#1d4ed8',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
    ),
    available: true,
  },
  {
    id: 'call',
    label: 'Voice Call',
    description: 'Automated voice verification',
    color: '#059669',
    hoverColor: '#047857',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
      </svg>
    ),
    available: true,
  },
];

export function VerificationMethodButtons({
  selectedMethod,
  onMethodSelect,
  phoneNumber,
  disabled = false,
  loading = false,
  layout = 'vertical',
  showDescriptions = true,
  className = '',
  'data-testid': testId,
}: VerificationMethodButtonsProps) {
  const { colors } = useThemeStore();
  const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex flex-row gap-3';
      case 'grid':
        return 'grid grid-cols-1 sm:grid-cols-3 gap-3';
      default:
        return 'flex flex-col gap-3';
    }
  };

  const getButtonClasses = (method: VerificationMethod, isSelected: boolean) => {
    const baseClasses = `
      relative group transition-all duration-200 ease-out
      rounded-xl border-2 p-4 cursor-pointer
      ${layout === 'horizontal' ? 'flex-1' : 'w-full'}
      ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-[1.02] active:scale-[0.98]'}
      ${isSelected ? 'ring-2 ring-offset-2' : ''}
    `;

    return baseClasses;
  };

  const handleMethodSelect = (method: VerificationMethod) => {
    if (disabled || loading || !method.available) return;
    onMethodSelect(method.id);
  };

  return (
    <div className={`${className}`} data-testid={testId}>
      {phoneNumber && (
        <div className="mb-4 p-3 rounded-lg bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            <span className="text-sm text-slate-600 dark:text-slate-400">
              Verification will be sent to: <strong className="text-slate-900 dark:text-slate-100">{phoneNumber}</strong>
            </span>
          </div>
        </div>
      )}

      <div className={getLayoutClasses()}>
        {verificationMethods.map((method) => {
          const isSelected = selectedMethod === method.id;
          const isHovered = hoveredMethod === method.id;
          
          return (
            <button
              key={method.id}
              onClick={() => handleMethodSelect(method)}
              onMouseEnter={() => setHoveredMethod(method.id)}
              onMouseLeave={() => setHoveredMethod(null)}
              disabled={disabled || loading || !method.available}
              className={getButtonClasses(method, isSelected)}
              style={{
                backgroundColor: isSelected 
                  ? `${method.color}10` 
                  : isHovered 
                    ? `${method.color}05` 
                    : colors.surface,
                borderColor: isSelected 
                  ? method.color 
                  : isHovered 
                    ? `${method.color}60` 
                    : colors.border,
                ringColor: isSelected ? method.color : 'transparent',
              }}
              data-testid={`verification-method-${method.id}`}
            >
              <div className="flex items-center gap-3">
                {/* Icon */}
                <div
                  className={`
                    flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center
                    transition-all duration-200
                    ${isSelected ? 'scale-110' : isHovered ? 'scale-105' : ''}
                  `}
                  style={{
                    backgroundColor: isSelected || isHovered ? method.color : `${method.color}20`,
                    color: isSelected || isHovered ? 'white' : method.color,
                  }}
                >
                  {method.icon}
                </div>

                {/* Content */}
                <div className="flex-1 text-left">
                  <div className="flex items-center gap-2">
                    <h3 
                      className="font-semibold transition-colors duration-200"
                      style={{
                        color: isSelected ? method.color : colors.text,
                      }}
                    >
                      {method.label}
                    </h3>
                    {!method.available && (
                      <span className="text-xs px-2 py-1 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-500">
                        Soon
                      </span>
                    )}
                  </div>
                  
                  {showDescriptions && (
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      {method.description}
                    </p>
                  )}
                </div>

                {/* Selection indicator */}
                {isSelected && (
                  <div className="flex-shrink-0">
                    <div
                      className="w-6 h-6 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: method.color }}
                    >
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>

              {/* Loading indicator */}
              {loading && isSelected && (
                <div className="absolute inset-0 bg-white/50 dark:bg-slate-900/50 rounded-xl flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-slate-300 border-t-blue-600 rounded-full animate-spin" />
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Helper text */}
      <div className="mt-4 text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">
          Choose your preferred verification method
        </p>
      </div>
    </div>
  );
}
