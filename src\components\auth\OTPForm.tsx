import React, { useState, useEffect, useRef } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { Input, Button } from '../ui';
import { CountrySelector, type Country } from './CountrySelector';

export interface OTPFormProps {
  onSubmit?: (data: { phone: string; otp: string }) => void;
  onSendOTP?: (phone: string, method: 'whatsapp' | 'sms') => void;
  onBack?: () => void;
  loading?: boolean;
  sendingOTP?: boolean;
  error?: string;
  otpSent?: boolean;
  resendCooldown?: number;
  className?: string;
  'data-testid'?: string;
}

export function OTPForm({
  onSubmit,
  onSendOTP,
  onBack,
  loading = false,
  sendingOTP = false,
  error,
  otpSent = false,
  resendCooldown = 0,
  className = '',
  'data-testid': testId,
}: OTPFormProps) {
  const { colors } = useThemeStore();
  const [phone, setPhone] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<Country | null>({
    code: 'US',
    name: 'United States',
    dialCode: '+1',
    flag: '🇺🇸'
  });
  const [otp, setOTP] = useState(['', '', '', '', '', '']);
  const [otpMethod, setOTPMethod] = useState<'whatsapp' | 'sms'>('whatsapp');
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [countdown, setCountdown] = useState(resendCooldown);

  const otpRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (resendCooldown > 0) {
      setCountdown(resendCooldown);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [resendCooldown]);

  const validatePhone = () => {
    const errors: Record<string, string> = {};
    const phoneRegex = /^\d{10,15}$/;

    if (!phone) {
      errors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateOTP = () => {
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      setFieldErrors({ otp: 'Please enter the complete 6-digit code' });
      return false;
    }
    setFieldErrors({});
    return true;
  };

  const handleSendOTP = () => {
    if (validatePhone() && onSendOTP && selectedCountry) {
      onSendOTP(selectedCountry.dialCode + phone, otpMethod);
    }
  };

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) return; // Prevent multiple characters

    const newOTP = [...otp];
    newOTP[index] = value;
    setOTP(newOTP);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }

    // Clear error when user starts typing
    if (fieldErrors.otp) {
      setFieldErrors(prev => ({ ...prev, otp: '' }));
    }
  };

  const handleOTPKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateOTP() && onSubmit && selectedCountry) {
      onSubmit({ phone: selectedCountry.dialCode + phone, otp: otp.join('') });
    }
  };

  const PhoneIcon = () => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  );

  const WhatsAppIcon = () => (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
    </svg>
  );


  if (!otpSent) {
    return (
      <div className={`space-y-6 ${className}`} data-testid={testId}>
        {error && (
          <div
            className="p-4 rounded-lg border text-sm"
            style={{
              backgroundColor: `${colors.error}10`,
              borderColor: colors.error,
              color: colors.error,
            }}
          >
            {error}
          </div>
        )}

        {/* Method Selection - Creative Design */}
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-4">
            Choose verification method
          </label>
          <div className="grid grid-cols-2 gap-4">
            <button
              type="button"
              onClick={() => setOTPMethod('whatsapp')}
              className={`
                relative group p-4 rounded-xl border-2 transition-all duration-300
                ${otpMethod === 'whatsapp'
                  ? 'border-green-500 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/20 shadow-lg shadow-green-500/20'
                  : 'border-slate-200 dark:border-slate-700 hover:border-green-300 dark:hover:border-green-600 hover:shadow-md'
                }
              `}
            >
              <div className="flex flex-col items-center space-y-2">
                <div className={`
                  p-3 rounded-full transition-all duration-300
                  ${otpMethod === 'whatsapp'
                    ? 'bg-green-500 text-white scale-110'
                    : 'bg-slate-100 dark:bg-slate-800 text-green-500 group-hover:bg-green-50 dark:group-hover:bg-green-900/20'
                  }
                `}>
                  <WhatsAppIcon />
                </div>
                <span className={`text-sm font-semibold transition-colors ${
                  otpMethod === 'whatsapp' ? 'text-green-700 dark:text-green-300' : 'text-slate-700 dark:text-slate-300'
                }`}>
                  WhatsApp
                </span>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  Instant delivery
                </span>
              </div>
              {otpMethod === 'whatsapp' && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
            
            <button
              type="button"
              onClick={() => setOTPMethod('sms')}
              className={`
                relative group p-4 rounded-xl border-2 transition-all duration-300
                ${otpMethod === 'sms'
                  ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 shadow-lg shadow-blue-500/20'
                  : 'border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md'
                }
              `}
            >
              <div className="flex flex-col items-center space-y-2">
                <div className={`
                  p-3 rounded-full transition-all duration-300
                  ${otpMethod === 'sms'
                    ? 'bg-blue-500 text-white scale-110'
                    : 'bg-slate-100 dark:bg-slate-800 text-blue-500 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20'
                  }
                `}>
                  <PhoneIcon />
                </div>
                <span className={`text-sm font-semibold transition-colors ${
                  otpMethod === 'sms' ? 'text-blue-700 dark:text-blue-300' : 'text-slate-700 dark:text-slate-300'
                }`}>
                  SMS
                </span>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  Text message
                </span>
              </div>
              {otpMethod === 'sms' && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          </div>
        </div>

        {/* Phone Number with Country Selector */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
            Phone Number
          </label>
          <div className="grid grid-cols-5 gap-3">
            <div className="col-span-2">
              <CountrySelector
                value={selectedCountry}
                onChange={setSelectedCountry}
                placeholder="Country"
                disabled={sendingOTP}
                data-testid="country-selector"
              />
            </div>
            <div className="col-span-3">
              <Input
                type="tel"
                placeholder="Enter phone number"
                value={phone}
                onChange={e => setPhone(e.target.value.replace(/\D/g, ''))}
                error={fieldErrors.phone}
                startIcon={<PhoneIcon />}
                fullWidth
                disabled={sendingOTP}
                data-testid="otp-phone"
              />
            </div>
          </div>
          {selectedCountry && (
            <div className="text-xs text-slate-500 dark:text-slate-400 flex items-center gap-2">
              <span className="text-base">{selectedCountry.flag}</span>
              <span>
                Complete number: {selectedCountry.dialCode} {phone || 'XXXXXXXXXX'}
              </span>
            </div>
          )}
        </div>

        <div className="flex gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={sendingOTP}
          >
            Back
          </Button>
          <Button
            type="button"
            variant="primary"
            onClick={handleSendOTP}
            loading={sendingOTP}
            disabled={sendingOTP}
            fullWidth
            data-testid="send-otp"
          >
            Send {otpMethod === 'whatsapp' ? 'WhatsApp' : 'SMS'} Code
          </Button>
        </div>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className={`space-y-6 ${className}`}
      data-testid={testId}
    >
      {error && (
        <div
          className="p-4 rounded-lg border text-sm"
          style={{
            backgroundColor: `${colors.error}10`,
            borderColor: colors.error,
            color: colors.error,
          }}
        >
          {error}
        </div>
      )}

      <div className="text-center">
        <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
          Enter the 6-digit code sent to {selectedCountry?.dialCode}
          {phone} via {otpMethod === 'whatsapp' ? 'WhatsApp' : 'SMS'}
        </p>
      </div>

      {/* Creative OTP Input */}
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 text-center">
          Verification Code
        </label>
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-full h-px bg-gradient-to-r from-transparent via-slate-200 dark:via-slate-700 to-transparent"></div>
          </div>
          
          {/* OTP Input Grid */}
          <div className="relative bg-white dark:bg-slate-900 px-4">
            <div className="flex gap-3 justify-center">
              {otp.map((digit, index) => (
                <div key={index} className="relative group">
                  <input
                    ref={el => {
                      otpRefs.current[index] = el;
                    }}
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    value={digit}
                    onChange={e => handleOTPChange(index, e.target.value)}
                    onKeyDown={e => handleOTPKeyDown(index, e)}
                    className={`
                      w-14 h-14 text-center text-xl font-bold rounded-xl border-2 transition-all duration-300
                      bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900
                      ${digit
                        ? 'border-green-400 shadow-lg shadow-green-400/20 text-green-600 dark:text-green-400'
                        : 'border-slate-300 dark:border-slate-600 text-slate-900 dark:text-slate-100'
                      }
                      focus:outline-none focus:border-blue-500 focus:shadow-lg focus:shadow-blue-500/20
                      hover:border-blue-300 dark:hover:border-blue-600
                      disabled:opacity-50 disabled:cursor-not-allowed
                      transform hover:scale-105 focus:scale-105
                    `}
                    disabled={loading}
                    data-testid={`otp-digit-${index}`}
                  />
                  
                  {/* Animated dot indicator */}
                  <div className={`
                    absolute -bottom-2 left-1/2 transform -translate-x-1/2
                    w-2 h-2 rounded-full transition-all duration-300
                    ${digit
                      ? 'bg-green-400 scale-100 opacity-100'
                      : 'bg-slate-300 dark:bg-slate-600 scale-75 opacity-50'
                    }
                  `} />
                  
                  {/* Connection line to next input */}
                  {index < otp.length - 1 && (
                    <div className={`
                      absolute top-1/2 -right-6 w-3 h-px transition-all duration-300
                      ${digit && otp[index + 1]
                        ? 'bg-green-400'
                        : 'bg-slate-300 dark:bg-slate-600'
                      }
                    `} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {fieldErrors.otp && (
          <p className="mt-3 text-sm text-red-600 text-center flex items-center justify-center gap-2">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {fieldErrors.otp}
          </p>
        )}
      </div>

      {/* Resend */}
      <div className="text-center">
        {countdown > 0 ? (
          <p className="text-sm text-slate-500">Resend code in {countdown}s</p>
        ) : (
          <button
            type="button"
            onClick={handleSendOTP}
            className="text-sm font-medium transition-colors"
            style={{ color: colors.primary }}
            disabled={loading}
          >
            Resend code
          </button>
        )}
      </div>

      <div className="flex gap-3">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
          disabled={loading}
          fullWidth
          data-testid="verify-otp"
        >
          Verify Code
        </Button>
      </div>
    </form>
  );
}
