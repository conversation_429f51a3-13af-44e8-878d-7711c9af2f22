import type { Meta, StoryObj } from '@storybook/react';
import { GlobalThemeToggle } from './GlobalThemeToggle';

const meta: Meta<typeof GlobalThemeToggle> = {
  title: 'Components/GlobalThemeToggle',
  component: GlobalThemeToggle,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A global theme toggle that remains visible at all times and provides comprehensive theme switching.',
      },
    },
  },
  argTypes: {
    position: {
      control: 'select',
      options: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
      description: 'Position of the theme toggle on screen',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the theme toggle button',
    },
    showLabel: {
      control: 'boolean',
      description: 'Show theme label below the button',
    },
  },
};

export default meta;
type Story = StoryObj<typeof GlobalThemeToggle>;

const DemoContent = () => (
  <div className="min-h-screen bg-slate-50 dark:bg-slate-900 p-8">
    <div className="max-w-4xl mx-auto space-y-8">
      <header className="text-center">
        <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4">
          Global Theme Toggle Demo
        </h1>
        <p className="text-lg text-slate-600 dark:text-slate-400">
          Click the theme toggle to see the comprehensive theming in action
        </p>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Card 1 */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700">
          <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-3">
            Primary Card
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            This card demonstrates the primary color scheme and how it adapts to different themes.
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
            Primary Button
          </button>
        </div>

        {/* Card 2 */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700">
          <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-3">
            Secondary Card
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            Secondary elements maintain consistency across theme changes.
          </p>
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md transition-colors">
            Secondary Button
          </button>
        </div>

        {/* Card 3 */}
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700">
          <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-3">
            Status Indicators
          </h3>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-slate-700 dark:text-slate-300">Success</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-slate-700 dark:text-slate-300">Warning</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-slate-700 dark:text-slate-300">Error</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-8 border border-slate-200 dark:border-slate-700">
        <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-6">
          Form Elements
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Text Input
            </label>
            <input
              type="text"
              placeholder="Enter text here..."
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Select Dropdown
            </label>
            <select className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
          </div>
        </div>
      </div>

      <footer className="text-center text-slate-500 dark:text-slate-400 py-8">
        <p>Theme toggle is positioned and ready for interaction</p>
      </footer>
    </div>
  </div>
);

export const TopRight: Story = {
  args: {
    position: 'top-right',
    size: 'md',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const TopLeft: Story = {
  args: {
    position: 'top-left',
    size: 'md',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const BottomRight: Story = {
  args: {
    position: 'bottom-right',
    size: 'md',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const BottomLeft: Story = {
  args: {
    position: 'bottom-left',
    size: 'md',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const WithLabel: Story = {
  args: {
    position: 'top-right',
    size: 'md',
    showLabel: true,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const SmallSize: Story = {
  args: {
    position: 'top-right',
    size: 'sm',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};

export const LargeSize: Story = {
  args: {
    position: 'top-right',
    size: 'lg',
    showLabel: false,
  },
  render: (args) => (
    <>
      <DemoContent />
      <GlobalThemeToggle {...args} />
    </>
  ),
};
