import useSWR, { useSWRConfig } from 'swr';
import { useCallback } from 'react';

// Custom hook for API calls with consistent error handling
export function useAPI<T = any>(url: string | null, options?: any) {
  const { data, error, isLoading, mutate } = useSWR<T>(url, options);

  return {
    data,
    error,
    isLoading,
    isError: !!error,
    mutate,
  };
}

// Hook for user data
export function useUser(userId?: string) {
  const url = userId ? `/users/${userId}` : '/user';
  return useAPI(url);
}

// Hook for users list
export function useUsers() {
  return useAPI('/users');
}

// Hook for creating/updating data with optimistic updates
export function useOptimisticMutation<T = any>() {
  const { mutate } = useSWRConfig();

  const mutateOptimistic = useCallback(
    async (
      key: string,
      updateFn: () => Promise<T>,
      optimisticData?: T,
      options?: {
        rollbackOnError?: boolean;
        revalidate?: boolean;
      }
    ) => {
      const { rollbackOnError = true, revalidate = true } = options || {};

      try {
        // Optimistically update the data
        if (optimisticData) {
          await mutate(key, optimisticData, { revalidate: false });
        }

        // Perform the actual update
        const result = await updateFn();

        // Update with the real data
        await mutate(key, result, { revalidate });

        return result;
      } catch (error) {
        // Rollback on error if specified
        if (rollbackOnError) {
          await mutate(key, undefined, { revalidate: true });
        }
        throw error;
      }
    },
    [mutate]
  );

  return { mutateOptimistic, mutate };
}

// Hook for infinite loading/pagination
export function useInfiniteAPI<T = any>(
  getKey: (pageIndex: number, previousPageData: T | null) => string | null
) {
  const { data, error, isLoading, size, setSize, mutate } = useSWR(getKey);

  const isLoadingMore =
    isLoading || (size > 0 && data && typeof data[size - 1] === 'undefined');
  const isEmpty = data?.[0]?.length === 0;
  const isReachingEnd = isEmpty || (data && data[data.length - 1]?.length < 10); // Assuming 10 items per page

  return {
    data,
    error,
    isLoading,
    isLoadingMore,
    isEmpty,
    isReachingEnd,
    size,
    setSize,
    mutate,
  };
}

// Hook for real-time data with polling
export function useRealTimeAPI<T = any>(
  url: string | null,
  interval: number = 5000
) {
  return useAPI<T>(url, {
    refreshInterval: interval,
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
  });
}

// Hook for cached data that rarely changes
export function useStaticAPI<T = any>(url: string | null) {
  return useAPI<T>(url, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    refreshInterval: 0,
    dedupingInterval: 60000, // 1 minute
  });
}

// Hook for authentication
export function useAuth() {
  const { data: user, error, isLoading, mutate } = useAPI('/user');

  const login = useCallback(
    async (credentials: { email: string; password: string }) => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Login failed');
      }

      const data = await response.json();
      // Update the user cache
      await mutate(data.user, { revalidate: false });
      return data;
    },
    [mutate]
  );

  const logout = useCallback(async () => {
    await fetch('/api/auth/logout', { method: 'POST' });
    // Clear the user cache
    await mutate(null, { revalidate: false });
  }, [mutate]);

  return {
    user,
    isLoading,
    isAuthenticated: !!user && !error,
    error,
    login,
    logout,
    mutate,
  };
}

// Hook for health check
export function useHealthCheck() {
  return useAPI('/health', {
    refreshInterval: 30000, // Check every 30 seconds
    revalidateOnFocus: false,
  });
}
