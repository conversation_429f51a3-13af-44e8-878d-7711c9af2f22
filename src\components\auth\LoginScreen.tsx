import { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { GlobalThemeToggle } from '../GlobalThemeToggle';
import { Logo } from '../common';
import { LoginForm } from './LoginForm';
import { OTPForm } from './OTPForm';
import { ForgotPasswordForm } from './ForgotPasswordForm';
import { AccessRequestForm } from './AccessRequestForm';
import { SocialLoginButtons, SocialLoginDivider } from './SocialLoginButtons';
import { CaptchaComponent } from '../security/CaptchaComponent';
import { DevLoginModal } from './DevLoginModal';
import { ERPFeaturesSlider } from './ERPFeaturesSlider';
import { AuthNavigationTabs } from './AuthNavigationTabs';

export interface LoginScreenProps {
  className?: string;
  onLogin?: (credentials: any) => void;
  onAccessRequest?: (request: any) => void;
  'data-testid'?: string;
}

export type LoginMode = 'login' | 'otp' | 'forgot' | 'access-request';

export function LoginScreen({
  className = '',
  onLogin,
  onAccessRequest,
  'data-testid': testId,
}: LoginScreenProps) {
  const { colors } = useThemeStore();
  const [isLeftSide, setIsLeftSide] = useState(true);
  const [loginMode, setLoginMode] = useState<LoginMode>('login');
  const [isAnimating, setIsAnimating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [forgotPasswordStep, setForgotPasswordStep] = useState<
    'method' | 'otp' | 'reset'
  >('method');
  const [showSocialLogin, setShowSocialLogin] = useState(false);
  const [showDevLogin, setShowDevLogin] = useState(false);


  const handleSideSwap = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => {
      setIsLeftSide(!isLeftSide);
      setIsAnimating(false);
    }, 400);
  };

  const handleLogin = async (credentials: any) => {
    setLoading(true);
    setError('');

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLogin?.(credentials);
    } catch (err) {
      setError('Login failed. Please try again.');
      setShowCaptcha(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    setLoading(true);
    setError('');

    // Simulate social login
    setTimeout(() => {
      setLoading(false);
      console.log(`${provider} login initiated`);
    }, 1000);
  };

  const handleOTPSend = async (_phone: string, _method: 'whatsapp' | 'sms') => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOtpSent(true);
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (_data: { method: 'whatsapp' | 'email' | 'mobile'; value: string; }) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setForgotPasswordStep('otp');
    } catch (err) {
      setError('Failed to send reset code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAccessRequest = async (data: any) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      onAccessRequest?.(data);
    } catch (err) {
      setError('Failed to submit request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const FormContainer = () => (
    <div className="h-full bg-white dark:bg-slate-900 flex flex-col relative">
      {/* Fixed Header */}
      <div className="flex-shrink-0 p-4 sm:p-6 lg:p-8 border-b border-slate-200/50 dark:border-slate-700/50">
        <div className="max-w-md mx-auto">
          {/* Logo */}
          <div className="flex justify-center mb-6">
            <Logo
              size="lg"
              variant="full"
              showText={true}
              data-testid="login-logo"
            />
          </div>

          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2 text-center">
            {loginMode === 'login' && 'Sign In'}
            {loginMode === 'otp' && 'Verify OTP'}
            {loginMode === 'forgot' && 'Reset Password'}
            {loginMode === 'access-request' && 'Request Access'}
          </h2>
          <p className="text-sm sm:text-base text-slate-600 dark:text-slate-400 text-center">
            {loginMode === 'login' &&
              'Enter your credentials to access your account'}
            {loginMode === 'otp' &&
              'Enter the verification code sent to your device'}
            {loginMode === 'forgot' && 'Enter your email to reset your password'}
            {loginMode === 'access-request' &&
              'Fill out the form below to request access'}
          </p>
        </div>
      </div>

      {/* Scrollable Form Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4 sm:p-6 lg:p-8 pb-20"> {/* Extra bottom padding for mobile */}
          <div className="max-w-md mx-auto">
            <div
              className={`transition-all duration-300 ${isAnimating ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}`}
            >
              {loginMode === 'login' && (
                <>
                  <LoginForm
                    onSubmit={handleLogin}
                    onForgotPassword={() => setLoginMode('forgot')}
                    onSwitchToOTP={() => setLoginMode('otp')}
                    loading={loading}
                    error={error}
                  />

                  {showCaptcha && !captchaVerified && (
                    <div className="mt-6">
                      <CaptchaComponent
                        onVerify={_token => {
                          setCaptchaVerified(true);
                          setShowCaptcha(false);
                        }}
                        onError={error => setError(error)}
                      />
                    </div>
                  )}

                  {/* Collapsible Social Login */}
                  <div className="mt-6 mb-8">
                    <button
                      type="button"
                      onClick={() => setShowSocialLogin(!showSocialLogin)}
                      className="w-full text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors border-none bg-transparent p-2"
                    >
                      {showSocialLogin ? 'Hide' : 'Show'} social login options
                    </button>

                    {showSocialLogin && (
                      <div className="mt-4 space-y-4">
                        <SocialLoginDivider />
                        <SocialLoginButtons
                          layout="grid"
                          showLabels={false}
                          loading={loading}
                          onGoogleLogin={() => handleSocialLogin('Google')}
                          onFacebookLogin={() => handleSocialLogin('Facebook')}
                          onLinkedInLogin={() => handleSocialLogin('LinkedIn')}
                          onMicrosoftLogin={() => handleSocialLogin('Microsoft')}
                        />
                      </div>
                    )}
                  </div>
                </>
              )}

              {loginMode === 'otp' && (
                <div className="mb-8">
                  <OTPForm
                    onSubmit={data => console.log('OTP Login:', data)}
                    onSendOTP={handleOTPSend}
                    onBack={() => setLoginMode('login')}
                    loading={loading}
                    error={error}
                    otpSent={otpSent}
                  />
                </div>
              )}

              {loginMode === 'forgot' && (
                <div className="mb-8">
                  <ForgotPasswordForm
                    onSubmit={handleForgotPassword}
                    onBack={() => setLoginMode('login')}
                    onVerifyOTP={data => console.log('Password Reset:', data)}
                    loading={loading}
                    error={error}
                    step={forgotPasswordStep}
                  />
                </div>
              )}

              {loginMode === 'access-request' && (
                <div className="mb-8">
                  <AccessRequestForm
                    onSubmit={handleAccessRequest}
                    onBack={() => setLoginMode('login')}
                    loading={loading}
                    error={error}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Auth Navigation Tabs at bottom */}
      <div className="absolute bottom-0 left-0 right-0 z-50">
        <AuthNavigationTabs
          currentMode={loginMode}
          onModeChange={setLoginMode}
          disabled={loading}
        />
      </div>
    </div>
  );

  return (
    <div className={`min-h-screen flex ${className}`} data-testid={testId}>
      {/* Global Theme Toggle */}
      <GlobalThemeToggle position="top-right" />

      {/* Compact View Toggle Button */}
      <button
        onClick={handleSideSwap}
        disabled={isAnimating || loading}
        className={`
          fixed top-1/2 left-4 -translate-y-1/2 z-50
          w-10 h-16 rounded-full
          bg-slate-900/80 dark:bg-slate-100/80 backdrop-blur-sm
          border border-slate-700/50 dark:border-slate-300/50
          text-slate-100 dark:text-slate-900
          hover:bg-slate-800/90 dark:hover:bg-slate-200/90
          hover:scale-105 active:scale-95
          transition-all duration-300
          disabled:opacity-50 disabled:cursor-not-allowed
          shadow-lg hover:shadow-xl
          group overflow-hidden
        `}
        aria-label="Switch layout"
        title="Switch layout"
      >
        {/* Animated background indicator */}
        <div
          className={`
            absolute inset-0 rounded-full
            bg-gradient-to-r from-blue-500/20 to-purple-500/20
            transition-all duration-500
            ${isAnimating ? 'scale-110 opacity-100' : 'scale-0 opacity-0'}
          `}
        />
        
        {/* Icon container */}
        <div className="relative flex flex-col items-center justify-center h-full">
          {/* Top icon */}
          <div
            className={`
              w-3 h-3 rounded-sm mb-1
              transition-all duration-300
              ${isLeftSide ? 'bg-blue-400' : 'bg-slate-400 dark:bg-slate-600'}
              ${isAnimating ? 'scale-75 rotate-12' : 'scale-100 rotate-0'}
            `}
          />
          
          {/* Swap arrows */}
          <svg
            className={`w-4 h-4 transition-all duration-500 ${isAnimating ? 'rotate-180 scale-75' : 'rotate-0 scale-100'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
            />
          </svg>
          
          {/* Bottom icon */}
          <div
            className={`
              w-3 h-3 rounded-sm mt-1
              transition-all duration-300
              ${!isLeftSide ? 'bg-purple-400' : 'bg-slate-400 dark:bg-slate-600'}
              ${isAnimating ? 'scale-75 -rotate-12' : 'scale-100 rotate-0'}
            `}
          />
        </div>
      </button>

      {/* Left Side - ERP Features or Form */}
      <div
        className={`
          w-full lg:w-1/2
          transition-all
          duration-700
          ease-in-out
          transform-gpu
          ${isAnimating ? 'scale-95 opacity-60 blur-sm' : 'scale-100 opacity-100 blur-0'}
        `}
        style={{
          background: isLeftSide
            ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.accent} 100%)`
            : colors.background,
          boxShadow: isLeftSide ? `0 20px 40px ${colors.primary}20` : 'none',
        }}
      >
        <div
          className={`h-full transition-all duration-700 ${isAnimating ? 'translate-x-4' : 'translate-x-0'}`}
        >
          {isLeftSide ? (
            <ERPFeaturesSlider
              autoPlay={true}
              autoPlayInterval={6000}
              showDots={true}
              showArrows={true}
              className="w-full h-full"
            />
          ) : (
            <FormContainer />
          )}
        </div>
      </div>

      {/* Right Side */}
      <div
        className={`
          w-full lg:w-1/2
          transition-all
          duration-700
          ease-in-out
          transform-gpu
          ${isAnimating ? 'scale-95 opacity-60 blur-sm' : 'scale-100 opacity-100 blur-0'}
        `}
        style={{
          background: !isLeftSide
            ? `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.accent} 100%)`
            : colors.background,
          boxShadow: !isLeftSide ? `0 20px 40px ${colors.primary}20` : 'none',
        }}
      >
        <div
          className={`h-full transition-all duration-700 ${isAnimating ? '-translate-x-4' : 'translate-x-0'}`}
        >
          {!isLeftSide ? (
            <ERPFeaturesSlider
              autoPlay={true}
              autoPlayInterval={6000}
              showDots={true}
              showArrows={true}
              className="w-full h-full"
            />
          ) : (
            <FormContainer />
          )}
        </div>
      </div>

      {/* Dev Login Button - Only show in development */}
      {import.meta.env.DEV && (
        <button
          onClick={() => setShowDevLogin(true)}
          className="fixed bottom-6 left-6 z-50 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-lg shadow-lg transition-colors"
          title="Development Login Options"
        >
          🔧 Dev Login
        </button>
      )}

      {/* Dev Login Modal */}
      {showDevLogin && (
        <DevLoginModal
          isOpen={showDevLogin}
          onClose={() => setShowDevLogin(false)}
          onLogin={user => {
            setShowDevLogin(false);
            onLogin?.(user);
          }}
        />
      )}
    </div>
  );
}
