import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  neutral: string;
  success: string;
  warning: string;
  error: string;
  background: string;
  surface: string;
  surfaceSecondary: string;
  text: string;
  textSecondary: string;
  textMuted: string;
  border: string;
  borderSecondary: string;
  shadow: string;
  // Enhanced colors for better theming
  muted: string;
  destructive: string;
  ring: string;
  input: string;
  card: string;
  popover: string;
  // Semantic colors
  primaryForeground: string;
  secondaryForeground: string;
  mutedForeground: string;
  accentForeground: string;
  destructiveForeground: string;
  // State colors
  hover: string;
  active: string;
  disabled: string;
}

const lightTheme: ThemeColors = {
  primary: '#2563eb', // Professional blue-600
  secondary: '#4f46e5', // Professional indigo-600
  accent: '#7c3aed', // Professional violet-600
  neutral: '#6b7280', // Professional gray-500
  success: '#059669', // Professional emerald-600
  warning: '#d97706', // Professional amber-600
  error: '#dc2626', // Professional red-600
  background: '#ffffff', // Pure white
  surface: '#f9fafb', // Professional gray-50
  surfaceSecondary: '#f3f4f6', // Professional gray-100
  text: '#111827', // Professional gray-900
  textSecondary: '#6b7280', // Professional gray-500
  textMuted: '#9ca3af', // Professional gray-400
  border: '#e5e7eb', // Professional gray-200
  borderSecondary: '#d1d5db', // Professional gray-300
  shadow: 'rgba(0, 0, 0, 0.1)', // Subtle shadow
  // Enhanced colors
  muted: '#f3f4f6',
  destructive: '#dc2626',
  ring: '#2563eb',
  input: '#ffffff',
  card: '#ffffff',
  popover: '#ffffff',
  // Semantic colors
  primaryForeground: '#ffffff',
  secondaryForeground: '#ffffff',
  mutedForeground: '#6b7280',
  accentForeground: '#ffffff',
  destructiveForeground: '#ffffff',
  // State colors
  hover: '#f3f4f6',
  active: '#e5e7eb',
  disabled: '#f9fafb',
};

const darkTheme: ThemeColors = {
  primary: '#3b82f6', // Professional blue-500
  secondary: '#6366f1', // Professional indigo-500
  accent: '#8b5cf6', // Professional violet-500
  neutral: '#9ca3af', // Professional gray-400
  success: '#10b981', // Professional emerald-500
  warning: '#f59e0b', // Professional amber-500
  error: '#ef4444', // Professional red-500
  background: '#111827', // Professional gray-900
  surface: '#1f2937', // Professional gray-800
  surfaceSecondary: '#374151', // Professional gray-700
  text: '#f9fafb', // Professional gray-50
  textSecondary: '#d1d5db', // Professional gray-300
  textMuted: '#9ca3af', // Professional gray-400
  border: '#4b5563', // Professional gray-600
  borderSecondary: '#6b7280', // Professional gray-500
  shadow: 'rgba(0, 0, 0, 0.3)', // Stronger shadow for dark mode
  // Enhanced colors
  muted: '#374151',
  destructive: '#ef4444',
  ring: '#3b82f6',
  input: '#1f2937',
  card: '#1f2937',
  popover: '#1f2937',
  // Semantic colors
  primaryForeground: '#ffffff',
  secondaryForeground: '#ffffff',
  mutedForeground: '#9ca3af',
  accentForeground: '#ffffff',
  destructiveForeground: '#ffffff',
  // State colors
  hover: '#374151',
  active: '#4b5563',
  disabled: '#1f2937',
};

interface ThemeState {
  theme: Theme;
  colors: ThemeColors;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  getCSSVariables: () => Record<string, string>;
}

export const useThemeStore = create<ThemeState>()(
  devtools(
    persist(
      (set, get) => ({
        theme: 'system',
        colors: lightTheme,

        setTheme: (theme: Theme) => {
          const isDark =
            theme === 'dark' ||
            (theme === 'system' &&
              window.matchMedia('(prefers-color-scheme: dark)').matches);

          set({
            theme,
            colors: isDark ? darkTheme : lightTheme,
          });

          // Update document class for Tailwind dark mode
          if (isDark) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        },

        toggleTheme: () => {
          const { theme } = get();
          const newTheme = theme === 'light' ? 'dark' : 'light';
          get().setTheme(newTheme);
        },

        getCSSVariables: () => {
          const { colors } = get();
          return {
            '--color-primary': colors.primary,
            '--color-secondary': colors.secondary,
            '--color-accent': colors.accent,
            '--color-neutral': colors.neutral,
            '--color-success': colors.success,
            '--color-warning': colors.warning,
            '--color-error': colors.error,
            '--color-background': colors.background,
            '--color-surface': colors.surface,
            '--color-surface-secondary': colors.surfaceSecondary,
            '--color-text': colors.text,
            '--color-text-secondary': colors.textSecondary,
            '--color-text-muted': colors.textMuted,
            '--color-border': colors.border,
            '--color-border-secondary': colors.borderSecondary,
            '--color-shadow': colors.shadow,
          };
        },
      }),
      {
        name: 'theme-store',
        onRehydrateStorage: () => state => {
          // Apply theme on hydration
          if (state) {
            state.setTheme(state.theme);
          }
        },
      }
    ),
    {
      name: 'theme-store',
    }
  )
);
